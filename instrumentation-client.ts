// This file configures the initialization of Sentry on the browser/client side
// This is the new recommended way for Turbopack compatibility
// Replaces sentry.client.config.ts for better Turbopack support
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';

// Environment variables with fallbacks
const SENTRY_DSN = process.env['SENTRY_DSN'] || '';
const NODE_ENV = process.env['NODE_ENV'] || 'development';
const VERCEL_GIT_COMMIT_SHA =
  process.env['VERCEL_GIT_COMMIT_SHA'] || 'development';

Sentry.init({
  dsn: SENTRY_DSN,

  // Adjust this value in production, or use tracesSampler for greater control
  tracesSampleRate: 1,

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,

  replaysOnErrorSampleRate: 1.0,

  // This sets the sample rate to be 10%. You may want this to be 100% while
  // in development and sample at a lower rate in production
  replaysSessionSampleRate: 0.1,

  // You can remove this option if you're not planning to use the Sentry Session Replay feature:
  integrations: [
    Sentry.replayIntegration({
      // Additional Replay configuration goes in here, for example:
      maskAllText: true,
      blockAllMedia: true,
    }),
  ],

  // Error filtering and reporting
  beforeSend(event) {
    // Filter out development errors
    if (process.env.NODE_ENV === 'development') {
      // Don't send certain errors in development
      if (event.exception) {
        const error = event.exception.values?.[0];
        if (
          error?.type === 'ChunkLoadError' ||
          error?.value?.includes('Loading chunk')
        ) {
          return null;
        }
        // Show report dialog for exceptions in development
        if (event.event_id) {
          Sentry.showReportDialog({ eventId: event.event_id });
        }
      }
    }
    return event;
  },

  // Set user context
  initialScope: {
    tags: {
      component: 'client',
      turbopack: 'enabled', // 标记使用 Turbopack
    },
  },

  // Environment configuration
  environment: NODE_ENV,

  // Release tracking
  release: VERCEL_GIT_COMMIT_SHA,

  // Custom error tags
  beforeSendTransaction(event) {
    // Add custom tags to transactions
    event.tags = {
      ...event.tags,
      section: 'client',
      bundler: 'turbopack',
    };
    return event;
  },
});

// 新功能：路由导航监控 (Sentry 10.0.0+)
// 用于监控页面导航性能和错误
export const onRouterTransitionStart = Sentry.captureRouterTransitionStart;
