// This file configures the initialization of Sentry on the server side
// The config you add here will be used whenever the server handles a request
// https://docs.sentry.io/platforms/javascript/guides/nextjs/
import * as Sentry from '@sentry/nextjs';

// Sampling rate constants
const PRODUCTION_SAMPLE_RATE = 0.1;
const DEVELOPMENT_SAMPLE_RATE = 1.0;

// Environment variables with fallbacks
const SENTRY_DSN = process.env['SENTRY_DSN'] || '';
const NODE_ENV = process.env['NODE_ENV'] || 'development';
const VERCEL_GIT_COMMIT_SHA =
  process.env['VERCEL_GIT_COMMIT_SHA'] || 'development';

Sentry.init({
  dsn: SENTRY_DSN,

  // Adjust this value in production, or use tracesSampler for greater control
  tracesSampleRate: 1,

  // Setting this option to true will print useful information to the console while you're setting up Sentry.
  debug: false,

  // Uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: process.env.NODE_ENV === 'development',

  // Performance Monitoring
  integrations: [
    // Add integrations for performance monitoring
    // Note: nodeProfilingIntegration may not be available in all versions
  ],

  // Set user context for server-side
  initialScope: {
    tags: {
      component: 'server',
      turbopack: 'enabled', // 标记使用 Turbopack
    },
  },

  // Environment configuration
  environment: NODE_ENV,

  // Release tracking
  release: VERCEL_GIT_COMMIT_SHA,

  // Server-side error filtering
  beforeSend(event) {
    // Filter out certain server errors
    if (NODE_ENV === 'development') {
      // Don't send certain errors in development
      if (event.exception) {
        const error = event.exception.values?.[0];
        if (
          error?.type === 'ENOENT' ||
          error?.value?.includes('ECONNREFUSED')
        ) {
          return null;
        }
      }
    }
    return event;
  },

  // Custom server tags
  beforeSendTransaction(event) {
    // Add custom tags to server transactions
    event.tags = {
      ...event.tags,
      section: 'server',
      bundler: 'turbopack',
    };
    return event;
  },

  // Configure sampling for different transaction types
  tracesSampler: (samplingContext) => {
    // Sample API routes at a higher rate
    if (samplingContext.request?.url?.includes('/api/')) {
      return 1.0;
    }

    // Sample page requests at a lower rate in production
    if (NODE_ENV === 'production') {
      return PRODUCTION_SAMPLE_RATE;
    }

    // Sample everything in development
    return DEVELOPMENT_SAMPLE_RATE;
  },
});
