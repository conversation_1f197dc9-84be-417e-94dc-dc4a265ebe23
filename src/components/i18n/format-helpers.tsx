'use client';

import { memo } from 'react';

import { DateFormatOptions } from '@/types/i18n-enhanced';
import { useFormatter, useLocale, useTranslations } from 'next-intl';

interface FormatDateProps {
  date: Date | string | number;
  format?: 'short' | 'long' | 'relative';
  className?: string;
}

const MILLISECONDS_PER_DAY = 1000 * 60 * 60 * 24;

const FormatDateComponent = ({
  date,
  format = 'short',
  className,
}: FormatDateProps) => {
  const formatter = useFormatter();
  const locale = useLocale();
  const t = useTranslations('formatting.date');

  const dateObj = new Date(date);
  const now = new Date();
  const diffInDays = Math.floor(
    (now.getTime() - dateObj.getTime()) / MILLISECONDS_PER_DAY,
  );

  let formattedDate: string;

  if (format === 'relative') {
    if (diffInDays === 0) {
      formattedDate = t('today');
    } else if (diffInDays === 1) {
      formattedDate = t('yesterday');
    } else if (diffInDays === -1) {
      formattedDate = t('tomorrow');
    } else {
      formattedDate = formatter.dateTime(dateObj, {
        dateStyle: 'medium',
      });
    }
  } else {
    const options: Intl.DateTimeFormatOptions = {
      dateStyle: format === 'long' ? 'full' : 'medium',
    };

    if (format === 'long') {
      options.timeStyle = 'short';
    }

    formattedDate = formatter.dateTime(dateObj, options as DateFormatOptions);
  }

  return (
    <time
      dateTime={dateObj.toISOString()}
      className={className}
      lang={locale}
    >
      {formattedDate}
    </time>
  );
};

FormatDateComponent.displayName = 'FormatDate';
export const FormatDate = memo(FormatDateComponent);

interface FormatNumberProps {
  value: number;
  type?: 'currency' | 'percentage' | 'decimal';
  currency?: string;
  minimumFractionDigits?: number;
  maximumFractionDigits?: number;
  className?: string;
}

const FormatNumberComponent = ({
  value,
  type = 'decimal',
  currency = 'USD',
  minimumFractionDigits,
  maximumFractionDigits,
  className,
}: FormatNumberProps) => {
  const formatter = useFormatter();
  const locale = useLocale();

  let formattedNumber: string;

  switch (type) {
    case 'currency': {
      const currencyOptions: Intl.NumberFormatOptions = {
        style: 'currency',
        currency: locale === 'zh' ? 'CNY' : currency,
      };
      if (minimumFractionDigits !== undefined) {
        currencyOptions.minimumFractionDigits = minimumFractionDigits;
      }
      if (maximumFractionDigits !== undefined) {
        currencyOptions.maximumFractionDigits = maximumFractionDigits;
      }
      formattedNumber = formatter.number(value, currencyOptions as NumberFormatOptions);
      break;
    }
    case 'percentage': {
      const percentOptions: Intl.NumberFormatOptions = {
        style: 'percent',
      };
      if (minimumFractionDigits !== undefined) {
        percentOptions.minimumFractionDigits = minimumFractionDigits;
      }
      if (maximumFractionDigits !== undefined) {
        percentOptions.maximumFractionDigits = maximumFractionDigits;
      }
      formattedNumber = formatter.number(value / 100, percentOptions as NumberFormatOptions);
      break;
    }
    default: {
      const numberOptions: Intl.NumberFormatOptions = {};
      if (minimumFractionDigits !== undefined) {
        numberOptions.minimumFractionDigits = minimumFractionDigits;
      }
      if (maximumFractionDigits !== undefined) {
        numberOptions.maximumFractionDigits = maximumFractionDigits;
      }
      formattedNumber = formatter.number(value, numberOptions as NumberFormatOptions);
    }
  }

  return (
    <span
      className={className}
      lang={locale}
    >
      {formattedNumber}
    </span>
  );
};

FormatNumberComponent.displayName = 'FormatNumber';
export const FormatNumber = memo(FormatNumberComponent);

interface PluralProps {
  count: number;
  category: 'items' | 'users' | 'notifications';
  className?: string;
}

const PluralComponent = ({ count, category, className }: PluralProps) => {
  const t = useTranslations(`formatting.plurals.${category}`);

  const getPluralKey = (count: number): 'zero' | 'one' | 'other' => {
    if (count === 0) return 'zero';
    if (count === 1) return 'one';
    return 'other';
  };

  const pluralKey = getPluralKey(count);
  const message = t(pluralKey, { count });

  return <span className={className}>{message}</span>;
};

PluralComponent.displayName = 'Plural';
export const Plural = memo(PluralComponent);

interface RichTextProps {
  text: string;
  values?: Record<string, React.ReactNode>;
  className?: string;
}

const RichTextComponent = ({ text, values = {}, className }: RichTextProps) => {
  // Simple rich text processor for basic formatting
  const processText = (text: string): React.ReactNode => {
    // Replace {key} with values
    let processed = text;
    Object.entries(values).forEach(([key, value]) => {
      const placeholder = `{${key}}`;
      if (typeof value === 'string' || typeof value === 'number') {
        processed = processed.replace(
          new RegExp(placeholder, 'g'),
          String(value),
        );
      }
    });

    // Process basic markdown-like formatting
    return processed.split(/(\*\*.*?\*\*|\*.*?\*|`.*?`)/).map((part, index) => {
      if (part.startsWith('**') && part.endsWith('**')) {
        return <strong key={index}>{part.slice(2, -2)}</strong>;
      }
      if (part.startsWith('*') && part.endsWith('*')) {
        return <em key={index}>{part.slice(1, -1)}</em>;
      }
      if (part.startsWith('`') && part.endsWith('`')) {
        return (
          <code
            key={index}
            className='bg-muted rounded px-1'
          >
            {part.slice(1, -1)}
          </code>
        );
      }
      return part;
    });
  };

  return <span className={className}>{processText(text)}</span>;
};

RichTextComponent.displayName = 'RichText';
export const RichText = memo(RichTextComponent);
