'use client';

import { useAccessibility } from '@/lib/accessibility';
import React, { useCallback, useEffect, useState } from 'react';
import { useEnhancedTheme } from './use-enhanced-theme';

/**
 * 主题切换逻辑的自定义hook
 * 封装主题切换、无障碍性支持和状态管理
 */
export function useThemeToggle() {
  const { theme, setThemeWithCircularTransition, supportsViewTransitions } = useEnhancedTheme();
  const [isOpen, setIsOpenState] = useState(false);
  const [mounted, setMounted] = useState(false);

  const setIsOpen = setIsOpenState;
  const {
    announceThemeChange,
    announceSwitching,
    prefersReducedMotion,
    prefersHighContrast,
    handleKeyboardNavigation,
    getAriaAttributes,
  } = useAccessibility();

  // 确保组件已挂载，避免 SSR/CSR 不一致
  useEffect(() => {
    setMounted(true);
  }, []);

  // 处理主题切换
  const handleThemeChange = useCallback((newTheme: string, event?: React.MouseEvent<HTMLElement>) => {
    // 播报切换状态
    announceSwitching();

    // 执行主题切换
    setThemeWithCircularTransition(newTheme, event);

    // 延迟播报完成状态
    const reducedMotionDelay = 50;
    const normalDelay = 400;
    setTimeout(() => {
      announceThemeChange(newTheme);
    }, prefersReducedMotion ? reducedMotionDelay : normalDelay);

    // 关闭下拉菜单
    setIsOpen(false);
  }, [setThemeWithCircularTransition, announceSwitching, announceThemeChange, prefersReducedMotion, setIsOpen]);

  // 处理键盘导航
  const handleKeyDown = useCallback((event: React.KeyboardEvent, action: () => void) => {
    handleKeyboardNavigation(
      event.nativeEvent,
      action,
      () => setIsOpen(false)
    );
  }, [handleKeyboardNavigation]);

  // 获取当前主题的ARIA属性
  // 在组件挂载前使用 'system' 作为默认值以避免 SSR/CSR 不一致
  const currentTheme = mounted ? (theme || 'system') : 'system';
  const ariaAttributes = getAriaAttributes(currentTheme, isOpen);

  return {
    // 状态
    theme,
    isOpen,
    setIsOpen,
    supportsViewTransitions,
    prefersReducedMotion,
    prefersHighContrast,

    // 处理函数
    handleThemeChange,
    handleKeyDown,

    // ARIA属性
    ariaAttributes,
  };
}
