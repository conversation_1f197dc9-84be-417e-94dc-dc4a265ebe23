import * as themeAnalytics from '@/lib/theme-analytics';
import { act, renderHook } from '@testing-library/react';
import { useTheme } from 'next-themes';
import { useEnhancedTheme } from '../use-enhanced-theme';

// Mock next-themes
jest.mock('next-themes', () => ({
  useTheme: jest.fn(),
}));

// Mock theme analytics
jest.mock('@/lib/theme-analytics', () => ({
  recordThemeSwitch: jest.fn(),
  recordThemePreference: jest.fn(),
}));

// Mock performance.now
const mockPerformanceNow = jest.fn();
Object.defineProperty(global, 'performance', {
  value: {
    now: mockPerformanceNow,
  },
  writable: true,
});

describe('useEnhancedTheme', () => {
  const mockSetTheme = jest.fn();
  const mockThemeData = {
    theme: 'light',
    setTheme: mockSetTheme,
    themes: ['light', 'dark', 'system'],
    forcedTheme: undefined,
    resolvedTheme: 'light',
    systemTheme: 'light',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useTheme as jest.Mock).mockReturnValue(mockThemeData);
    mockPerformanceNow.mockReturnValue(1000);

    // Reset document properties (only if document exists and is not null)
    if (typeof document !== 'undefined' && document !== null && 'startViewTransition' in document) {
      delete (document as any).startViewTransition;
    }

    // Mock window properties (only if window exists)
    if (typeof window !== 'undefined') {
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 1024,
      });
      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 768,
      });
    }
  });

  describe('View Transitions API 浏览器兼容性检测', () => {
    it('should detect when View Transitions is not supported', () => {
      const { result } = renderHook(() => useEnhancedTheme());

      expect(result.current.supportsViewTransitions).toBe(false);
    });

    it('should detect when View Transitions is supported', () => {
      // Mock View Transitions support
      Object.defineProperty(document, 'startViewTransition', {
        value: jest.fn(),
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      expect(result.current.supportsViewTransitions).toBe(true);
    });



    it('should handle startViewTransition as non-function', () => {
      Object.defineProperty(document, 'startViewTransition', {
        value: 'not-a-function',
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      expect(result.current.supportsViewTransitions).toBe(false);
    });

    it('should handle startViewTransition as null', () => {
      Object.defineProperty(document, 'startViewTransition', {
        value: null,
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      expect(result.current.supportsViewTransitions).toBe(false);
    });

    it('should handle startViewTransition as undefined', () => {
      Object.defineProperty(document, 'startViewTransition', {
        value: undefined,
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      expect(result.current.supportsViewTransitions).toBe(false);
    });

    it('should handle document without startViewTransition property', () => {
      // Ensure the property doesn't exist
      if ('startViewTransition' in document) {
        delete (document as any).startViewTransition;
      }

      const { result } = renderHook(() => useEnhancedTheme());

      expect(result.current.supportsViewTransitions).toBe(false);
    });
  });

  describe('主题切换功能', () => {
    it('should fallback to original setTheme when View Transitions not supported', async () => {
      const { result } = renderHook(() => useEnhancedTheme());

      await act(async () => {
        result.current.setTheme('dark');
      });

      expect(mockSetTheme).toHaveBeenCalledWith('dark');
      expect(themeAnalytics.recordThemeSwitch).toHaveBeenCalledWith(
        'light',
        'dark',
        1000,
        1000,
        false
      );
    });

    it('should use View Transitions when supported', async () => {
      const mockTransition = {
        finished: Promise.resolve(),
      };
      const mockStartViewTransition = jest.fn().mockReturnValue(mockTransition);

      Object.defineProperty(document, 'startViewTransition', {
        value: mockStartViewTransition,
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      await act(async () => {
        result.current.setTheme('dark');
      });

      expect(mockStartViewTransition).toHaveBeenCalled();
      await mockTransition.finished;
      expect(themeAnalytics.recordThemeSwitch).toHaveBeenCalledWith(
        'light',
        'dark',
        1000,
        1000,
        true
      );
    });

    it('should handle View Transitions promise rejection', async () => {
      const rejectedPromise = Promise.reject(new Error('Transition failed'));
      // Handle the rejection to prevent unhandled promise rejection
      rejectedPromise.catch(() => {
        // Expected to fail
      });

      const mockTransition = {
        finished: rejectedPromise,
      };
      const mockStartViewTransition = jest.fn().mockReturnValue(mockTransition);

      Object.defineProperty(document, 'startViewTransition', {
        value: mockStartViewTransition,
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      await act(async () => {
        result.current.setTheme('dark');
      });

      expect(mockStartViewTransition).toHaveBeenCalled();

      // Should still record analytics even if transition fails
      try {
        await mockTransition.finished;
      } catch {
        // Expected to fail
      }

      expect(themeAnalytics.recordThemeSwitch).toHaveBeenCalled();
    });

    it('should handle startViewTransition throwing an error during execution', async () => {
      const mockStartViewTransition = jest.fn().mockImplementation(() => {
        throw new Error('startViewTransition execution failed');
      });

      Object.defineProperty(document, 'startViewTransition', {
        value: mockStartViewTransition,
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      // The current implementation doesn't have try-catch, so it should throw
      await act(async () => {
        expect(() => {
          result.current.setTheme('dark');
        }).toThrow('startViewTransition execution failed');
      });

      expect(mockStartViewTransition).toHaveBeenCalled();
    });

    it('should handle startViewTransition returning null', async () => {
      const mockStartViewTransition = jest.fn().mockReturnValue(null);

      Object.defineProperty(document, 'startViewTransition', {
        value: mockStartViewTransition,
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      // The current implementation doesn't check for null, so it should throw
      await act(async () => {
        expect(() => {
          result.current.setTheme('dark');
        }).toThrow();
      });

      expect(mockStartViewTransition).toHaveBeenCalled();
    });

    it('should handle startViewTransition returning undefined', async () => {
      const mockStartViewTransition = jest.fn().mockReturnValue(undefined);

      Object.defineProperty(document, 'startViewTransition', {
        value: mockStartViewTransition,
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      // The current implementation doesn't check for undefined, so it should throw
      await act(async () => {
        expect(() => {
          result.current.setTheme('dark');
        }).toThrow();
      });

      expect(mockStartViewTransition).toHaveBeenCalled();
    });

    it('should handle transition object without finished property', async () => {
      const mockTransition = {}; // Missing finished property
      const mockStartViewTransition = jest.fn().mockReturnValue(mockTransition);

      Object.defineProperty(document, 'startViewTransition', {
        value: mockStartViewTransition,
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      // The current implementation doesn't check for finished property, so it should throw
      await act(async () => {
        expect(() => {
          result.current.setTheme('dark');
        }).toThrow();
      });

      expect(mockStartViewTransition).toHaveBeenCalled();
    });
  });

  describe('圆形展开动画', () => {
    it('should calculate correct coordinates from click event', async () => {
      const mockTransition = {
        ready: Promise.resolve(),
        finished: Promise.resolve(),
      };
      const mockStartViewTransition = jest.fn().mockReturnValue(mockTransition);
      const mockAnimate = jest.fn();

      Object.defineProperty(document, 'startViewTransition', {
        value: mockStartViewTransition,
        writable: true,
        configurable: true,
      });

      Object.defineProperty(document.documentElement, 'animate', {
        value: mockAnimate,
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      const mockClickEvent = {
        clientX: 100,
        clientY: 200,
      } as React.MouseEvent<HTMLElement>;

      await act(async () => {
        result.current.setThemeWithCircularTransition('dark', mockClickEvent);
      });

      expect(mockStartViewTransition).toHaveBeenCalled();
      await mockTransition.ready;

      expect(mockAnimate).toHaveBeenCalledWith(
        expect.objectContaining({
          clipPath: expect.arrayContaining([
            'circle(0px at 100px 200px)',
            expect.stringMatching(/circle\(\d+px at 100px 200px\)/)
          ])
        }),
        expect.objectContaining({
          duration: 400,
          easing: 'ease-in-out',
          pseudoElement: '::view-transition-new(root)'
        })
      );
    });

    it('should use screen center when no click event provided', async () => {
      const mockTransition = {
        ready: Promise.resolve(),
        finished: Promise.resolve(),
      };
      const mockStartViewTransition = jest.fn().mockReturnValue(mockTransition);
      const mockAnimate = jest.fn();

      Object.defineProperty(document, 'startViewTransition', {
        value: mockStartViewTransition,
        writable: true,
        configurable: true,
      });

      Object.defineProperty(document.documentElement, 'animate', {
        value: mockAnimate,
        writable: true,
        configurable: true,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      await act(async () => {
        result.current.setThemeWithCircularTransition('dark');
      });

      expect(mockStartViewTransition).toHaveBeenCalled();
      await mockTransition.ready;

      // Should use screen center (512, 384)
      expect(mockAnimate).toHaveBeenCalledWith(
        expect.objectContaining({
          clipPath: expect.arrayContaining([
            'circle(0px at 512px 384px)',
            expect.stringMatching(/circle\(\d+px at 512px 384px\)/)
          ])
        }),
        expect.any(Object)
      );
    });
  });

  describe('边缘情况处理', () => {
    it('should handle missing theme gracefully', () => {
      (useTheme as jest.Mock).mockReturnValue({
        ...mockThemeData,
        theme: undefined,
      });

      const { result } = renderHook(() => useEnhancedTheme());

      expect(result.current.theme).toBeUndefined();
      expect(() => result.current.setTheme('dark')).not.toThrow();
    });

    it('should handle zero window dimensions', () => {
      Object.defineProperty(window, 'innerWidth', { value: 0, writable: true });
      Object.defineProperty(window, 'innerHeight', { value: 0, writable: true });

      const { result } = renderHook(() => useEnhancedTheme());

      expect(() => result.current.setThemeWithCircularTransition('dark')).not.toThrow();
    });
  });
});
